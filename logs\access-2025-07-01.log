[2025-07-01T18:41:17.627Z] [ACCESS] GET / - 200 - 13ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"13ms","timestamp":"2025-07-01T18:41:17.622Z"}
[2025-07-01T18:41:17.683Z] [ACCESS] GET /css/style.css - 200 - 6ms | {"method":"GET","url":"/css/style.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"6ms","timestamp":"2025-07-01T18:41:17.682Z"}
[2025-07-01T18:41:17.686Z] [ACCESS] GET /images/lesson1.jpg - 200 - 6ms | {"method":"GET","url":"/images/lesson1.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"6ms","timestamp":"2025-07-01T18:41:17.685Z"}
[2025-07-01T18:41:17.688Z] [ACCESS] GET /images/lesson2.jpg - 200 - 7ms | {"method":"GET","url":"/images/lesson2.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"7ms","timestamp":"2025-07-01T18:41:17.687Z"}
[2025-07-01T18:41:17.761Z] [ACCESS] GET /images/lesson4.jpg - 200 - 6ms | {"method":"GET","url":"/images/lesson4.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"6ms","timestamp":"2025-07-01T18:41:17.760Z"}
[2025-07-01T18:41:17.763Z] [ACCESS] GET /images/lesson3.jpg - 200 - 9ms | {"method":"GET","url":"/images/lesson3.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"9ms","timestamp":"2025-07-01T18:41:17.762Z"}
[2025-07-01T18:41:17.764Z] [ACCESS] GET /js/network-animation.js - 200 - 9ms | {"method":"GET","url":"/js/network-animation.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"9ms","timestamp":"2025-07-01T18:41:17.764Z"}
[2025-07-01T18:41:17.766Z] [ACCESS] GET /js/landing.js - 200 - 10ms | {"method":"GET","url":"/js/landing.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"10ms","timestamp":"2025-07-01T18:41:17.766Z"}
[2025-07-01T18:41:50.244Z] [ACCESS] GET / - 304 - 9ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"9ms","timestamp":"2025-07-01T18:41:50.243Z"}
[2025-07-01T18:41:50.580Z] [ACCESS] GET / - 304 - 2ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"2ms","timestamp":"2025-07-01T18:41:50.580Z"}
[2025-07-01T18:41:53.700Z] [ACCESS] GET /lessons - 200 - 5ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"5ms","timestamp":"2025-07-01T18:41:53.699Z"}
[2025-07-01T18:41:53.726Z] [ACCESS] GET /js/index.js - 200 - 2ms | {"method":"GET","url":"/js/index.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T18:41:53.724Z"}
[2025-07-01T18:41:53.738Z] [ACCESS] GET /check-student-auth - 200 - 2ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T18:41:53.737Z"}
[2025-07-01T18:41:54.359Z] [ACCESS] GET / - 200 - 578ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"578ms","timestamp":"2025-07-01T18:41:54.358Z"}
[2025-07-01T18:41:54.464Z] [ACCESS] GET /?page=1&limit=10&sort=newest - 200 - 100ms | {"method":"GET","url":"/?page=1&limit=10&sort=newest","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"100ms","timestamp":"2025-07-01T18:41:54.463Z"}
[2025-07-01T18:41:57.163Z] [ACCESS] GET /lessons - 304 - 1ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"1ms","timestamp":"2025-07-01T18:41:57.162Z"}
[2025-07-01T18:41:57.202Z] [ACCESS] GET /check-student-auth - 200 - 3ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T18:41:57.201Z"}
[2025-07-01T18:42:00.052Z] [ACCESS] GET /quizgame - 200 - 2ms | {"method":"GET","url":"/quizgame","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T18:42:00.051Z"}
[2025-07-01T18:42:00.084Z] [ACCESS] GET /js/quizgame.js - 200 - 4ms | {"method":"GET","url":"/js/quizgame.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-01T18:42:00.083Z"}
[2025-07-01T18:42:00.251Z] [ACCESS] GET /audio/5sec_1.mp3 - 206 - 15ms | {"method":"GET","url":"/audio/5sec_1.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"15ms","timestamp":"2025-07-01T18:42:00.250Z"}
[2025-07-01T18:42:00.257Z] [ACCESS] GET /audio/5sec_2.mp3 - 206 - 19ms | {"method":"GET","url":"/audio/5sec_2.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"19ms","timestamp":"2025-07-01T18:42:00.255Z"}
[2025-07-01T18:42:00.260Z] [ACCESS] GET /audio/5sec_3.mp3 - 206 - 22ms | {"method":"GET","url":"/audio/5sec_3.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"22ms","timestamp":"2025-07-01T18:42:00.258Z"}
[2025-07-01T18:42:00.265Z] [ACCESS] GET /audio/30sec_1.mp3 - 206 - 43ms | {"method":"GET","url":"/audio/30sec_1.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"43ms","timestamp":"2025-07-01T18:42:00.263Z"}
[2025-07-01T18:42:00.275Z] [ACCESS] GET /audio/correct_1.mp3 - 206 - 11ms | {"method":"GET","url":"/audio/correct_1.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"11ms","timestamp":"2025-07-01T18:42:00.273Z"}
[2025-07-01T18:42:00.278Z] [ACCESS] GET /audio/30sec_2.mp3 - 206 - 52ms | {"method":"GET","url":"/audio/30sec_2.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"52ms","timestamp":"2025-07-01T18:42:00.277Z"}
[2025-07-01T18:42:00.281Z] [ACCESS] GET /audio/30sec_3.mp3 - 206 - 51ms | {"method":"GET","url":"/audio/30sec_3.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"51ms","timestamp":"2025-07-01T18:42:00.280Z"}
[2025-07-01T18:42:00.283Z] [ACCESS] GET /audio/correct_2.mp3 - 206 - 14ms | {"method":"GET","url":"/audio/correct_2.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"14ms","timestamp":"2025-07-01T18:42:00.282Z"}
[2025-07-01T18:42:00.285Z] [ACCESS] GET /audio/correct_3.mp3 - 206 - 16ms | {"method":"GET","url":"/audio/correct_3.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"16ms","timestamp":"2025-07-01T18:42:00.284Z"}
[2025-07-01T18:42:00.291Z] [ACCESS] GET /audio/correct_4.mp3 - 206 - 19ms | {"method":"GET","url":"/audio/correct_4.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"19ms","timestamp":"2025-07-01T18:42:00.289Z"}
[2025-07-01T18:42:00.295Z] [ACCESS] GET /audio/correct_5.mp3 - 206 - 8ms | {"method":"GET","url":"/audio/correct_5.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"8ms","timestamp":"2025-07-01T18:42:00.294Z"}
[2025-07-01T18:42:00.299Z] [ACCESS] GET /audio/incorrect.mp3 - 206 - 11ms | {"method":"GET","url":"/audio/incorrect.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"11ms","timestamp":"2025-07-01T18:42:00.298Z"}
[2025-07-01T18:42:00.302Z] [ACCESS] GET /audio/points.mp3 - 206 - 13ms | {"method":"GET","url":"/audio/points.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"13ms","timestamp":"2025-07-01T18:42:00.300Z"}
[2025-07-01T18:42:03.417Z] [ACCESS] GET / - 304 - 3ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"3ms","timestamp":"2025-07-01T18:42:03.416Z"}
[2025-07-01T18:42:04.157Z] [ACCESS] GET /leaderboard - 200 - 1ms | {"method":"GET","url":"/leaderboard","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T18:42:04.156Z"}
[2025-07-01T18:42:04.197Z] [ACCESS] GET /?page=1&filter=all - 404 - 6ms | {"method":"GET","url":"/?page=1&filter=all","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"6ms","timestamp":"2025-07-01T18:42:04.196Z"}
[2025-07-01T18:42:06.180Z] [ACCESS] GET /leaderboard - 304 - 3ms | {"method":"GET","url":"/leaderboard","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"3ms","timestamp":"2025-07-01T18:42:06.179Z"}
[2025-07-01T18:42:06.220Z] [ACCESS] GET /?page=1&filter=all - 404 - 3ms | {"method":"GET","url":"/?page=1&filter=all","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T18:42:06.219Z"}
[2025-07-01T18:42:11.004Z] [ACCESS] GET /?page=1&filter=month - 404 - 3ms | {"method":"GET","url":"/?page=1&filter=month","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T18:42:11.003Z"}
[2025-07-01T18:42:11.864Z] [ACCESS] GET / - 304 - 1ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"1ms","timestamp":"2025-07-01T18:42:11.863Z"}
[2025-07-01T18:42:12.853Z] [ACCESS] GET /lessons - 304 - 1ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"1ms","timestamp":"2025-07-01T18:42:12.852Z"}
[2025-07-01T18:42:12.893Z] [ACCESS] GET /check-student-auth - 200 - 2ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T18:42:12.892Z"}
[2025-07-01T18:42:14.233Z] [ACCESS] GET /admin/login - 200 - 2ms | {"method":"GET","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T18:42:14.232Z"}
[2025-07-01T18:42:16.974Z] [ACCESS] POST /admin/login - 200 - 403ms | {"method":"POST","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"403ms","timestamp":"2025-07-01T18:42:16.973Z"}
[2025-07-01T18:42:17.546Z] [ACCESS] GET /js/drag-utils.js - 200 - 3ms | {"method":"GET","url":"/js/drag-utils.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T18:42:17.546Z"}
[2025-07-01T18:42:17.559Z] [ACCESS] GET /admin - 200 - 77ms | {"method":"GET","url":"/admin","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"77ms","timestamp":"2025-07-01T18:42:17.559Z"}
[2025-07-01T18:42:17.561Z] [ACCESS] GET /js/admin-list.js - 200 - 19ms | {"method":"GET","url":"/js/admin-list.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"19ms","timestamp":"2025-07-01T18:42:17.560Z"}
[2025-07-01T18:42:18.052Z] [ACCESS] GET /?page=1&limit=15&sort=az - 200 - 473ms | {"method":"GET","url":"/?page=1&limit=15&sort=az","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"473ms","timestamp":"2025-07-01T18:42:18.051Z"}
[2025-07-01T18:42:24.867Z] [ACCESS] GET /history - 200 - 67ms | {"method":"GET","url":"/history","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"67ms","timestamp":"2025-07-01T18:42:24.866Z"}
[2025-07-01T18:42:24.869Z] [ACCESS] GET /js/history.js - 200 - 14ms | {"method":"GET","url":"/js/history.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"14ms","timestamp":"2025-07-01T18:42:24.868Z"}
[2025-07-01T18:42:25.848Z] [ACCESS] GET /?page=1&limit=15&sort=time-desc - 200 - 676ms | {"method":"GET","url":"/?page=1&limit=15&sort=time-desc","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"676ms","timestamp":"2025-07-01T18:42:25.847Z"}
[2025-07-01T18:42:45.804Z] [ACCESS] GET /admin - 200 - 257ms | {"method":"GET","url":"/admin","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"257ms","timestamp":"2025-07-01T18:42:45.804Z"}
[2025-07-01T18:42:51.518Z] [ACCESS] GET / - 404 - 64ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"64ms","timestamp":"2025-07-01T18:42:51.518Z"}
[2025-07-01T18:43:04.638Z] [ACCESS] GET /admin/students - 200 - 284ms | {"method":"GET","url":"/admin/students","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"284ms","timestamp":"2025-07-01T18:43:04.638Z"}
[2025-07-01T18:43:04.709Z] [ACCESS] GET / - 404 - 67ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"67ms","timestamp":"2025-07-01T18:43:04.709Z"}
[2025-07-01T18:43:04.938Z] [ACCESS] GET / - 404 - 295ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"295ms","timestamp":"2025-07-01T18:43:04.938Z"}
[2025-07-01T18:43:09.365Z] [ACCESS] GET /admin/students - 200 - 71ms | {"method":"GET","url":"/admin/students","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"71ms","timestamp":"2025-07-01T18:43:09.365Z"}
[2025-07-01T18:43:09.425Z] [ACCESS] GET / - 404 - 63ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"63ms","timestamp":"2025-07-01T18:43:09.424Z"}
[2025-07-01T18:43:09.666Z] [ACCESS] GET / - 404 - 304ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"304ms","timestamp":"2025-07-01T18:43:09.665Z"}
[2025-07-01T18:43:13.441Z] [ACCESS] GET / - 304 - 71ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"71ms","timestamp":"2025-07-01T18:43:13.440Z"}
[2025-07-01T18:43:14.743Z] [ACCESS] GET /lessons - 304 - 74ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"74ms","timestamp":"2025-07-01T18:43:14.742Z"}
[2025-07-01T18:43:14.855Z] [ACCESS] GET /check-student-auth - 200 - 72ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"72ms","timestamp":"2025-07-01T18:43:14.853Z"}
[2025-07-01T18:43:16.535Z] [ACCESS] GET /js/lesson.js - 200 - 1ms | {"method":"GET","url":"/js/lesson.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T18:43:16.534Z"}
[2025-07-01T18:43:16.539Z] [ACCESS] GET /lesson/1748074653639 - 200 - 71ms | {"method":"GET","url":"/lesson/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"71ms","timestamp":"2025-07-01T18:43:16.538Z"}
[2025-07-01T18:43:16.590Z] [ACCESS] GET / - 404 - 66ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"66ms","timestamp":"2025-07-01T18:43:16.590Z"}
[2025-07-01T18:43:16.649Z] [ACCESS] GET /check-student-auth - 200 - 71ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"71ms","timestamp":"2025-07-01T18:43:16.647Z"}
[2025-07-01T18:43:17.016Z] [ACCESS] GET /1748074653639 - 200 - 362ms | {"method":"GET","url":"/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"362ms","timestamp":"2025-07-01T18:43:17.014Z"}
[2025-07-01T18:43:21.612Z] [ACCESS] GET /lesson/1748074653639 - 304 - 72ms | {"method":"GET","url":"/lesson/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"72ms","timestamp":"2025-07-01T18:43:21.611Z"}
[2025-07-01T18:43:21.704Z] [ACCESS] GET / - 404 - 73ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"73ms","timestamp":"2025-07-01T18:43:21.703Z"}
[2025-07-01T18:43:21.742Z] [ACCESS] GET /check-student-auth - 200 - 62ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"62ms","timestamp":"2025-07-01T18:43:21.741Z"}
[2025-07-01T18:43:26.673Z] [ACCESS] GET /?page=1&limit=10&sort=oldest - 200 - 307ms | {"method":"GET","url":"/?page=1&limit=10&sort=oldest","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"307ms","timestamp":"2025-07-01T18:43:26.672Z"}
[2025-07-01T18:43:29.373Z] [ACCESS] GET /lesson/1738739931367 - 200 - 62ms | {"method":"GET","url":"/lesson/1738739931367","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"62ms","timestamp":"2025-07-01T18:43:29.372Z"}
[2025-07-01T18:43:29.438Z] [ACCESS] GET / - 404 - 74ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"74ms","timestamp":"2025-07-01T18:43:29.437Z"}
[2025-07-01T18:43:29.476Z] [ACCESS] GET /check-student-auth - 200 - 61ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"61ms","timestamp":"2025-07-01T18:43:29.475Z"}
[2025-07-01T18:43:29.725Z] [ACCESS] GET /1738739931367 - 200 - 247ms | {"method":"GET","url":"/1738739931367","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"247ms","timestamp":"2025-07-01T18:43:29.724Z"}
[2025-07-01T18:44:22.391Z] [ACCESS] GET / - 304 - 317ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"317ms","timestamp":"2025-07-01T18:44:22.390Z"}
[2025-07-01T18:44:26.294Z] [ACCESS] GET /lessons - 304 - 67ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"67ms","timestamp":"2025-07-01T18:44:26.293Z"}
[2025-07-01T18:44:26.414Z] [ACCESS] GET /check-student-auth - 200 - 69ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"69ms","timestamp":"2025-07-01T18:44:26.413Z"}
[2025-07-01T18:44:35.334Z] [ACCESS] GET / - 304 - 69ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"69ms","timestamp":"2025-07-01T18:44:35.332Z"}
[2025-07-01T18:45:10.842Z] [ACCESS] GET /quizgame - 200 - 264ms | {"method":"GET","url":"/quizgame","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"264ms","timestamp":"2025-07-01T18:45:10.840Z"}
[2025-07-01T18:45:13.142Z] [ACCESS] GET /leaderboard - 304 - 60ms | {"method":"GET","url":"/leaderboard","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"60ms","timestamp":"2025-07-01T18:45:13.141Z"}
[2025-07-01T18:45:13.250Z] [ACCESS] GET /?page=1&filter=all - 404 - 63ms | {"method":"GET","url":"/?page=1&filter=all","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"63ms","timestamp":"2025-07-01T18:45:13.249Z"}
[2025-07-01T18:45:15.752Z] [ACCESS] GET / - 404 - 62ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"62ms","timestamp":"2025-07-01T18:45:15.750Z"}
[2025-07-01T18:45:17.986Z] [ACCESS] GET /leaderboard - 304 - 60ms | {"method":"GET","url":"/leaderboard","ip":"::1","userAgent":"Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","statusCode":304,"responseTime":"60ms","timestamp":"2025-07-01T18:45:17.986Z"}
[2025-07-01T18:45:18.088Z] [ACCESS] GET / - 404 - 66ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"66ms","timestamp":"2025-07-01T18:45:18.082Z"}
[2025-07-01T18:45:18.216Z] [ACCESS] GET /?page=1&filter=all - 404 - 68ms | {"method":"GET","url":"/?page=1&filter=all","ip":"::1","userAgent":"Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","statusCode":404,"responseTime":"68ms","timestamp":"2025-07-01T18:45:18.216Z"}
[2025-07-01T18:45:43.603Z] [ACCESS] GET / - 304 - 258ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"258ms","timestamp":"2025-07-01T18:45:43.602Z"}
[2025-07-01T18:45:45.208Z] [ACCESS] GET /lessons - 304 - 53ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"53ms","timestamp":"2025-07-01T18:45:45.207Z"}
[2025-07-01T18:45:45.312Z] [ACCESS] GET /check-student-auth - 200 - 55ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"55ms","timestamp":"2025-07-01T18:45:45.312Z"}
[2025-07-01T18:45:48.452Z] [ACCESS] GET /lesson/1748074653639 - 304 - 55ms | {"method":"GET","url":"/lesson/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"55ms","timestamp":"2025-07-01T18:45:48.451Z"}
[2025-07-01T18:45:48.533Z] [ACCESS] GET / - 404 - 56ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"56ms","timestamp":"2025-07-01T18:45:48.532Z"}
[2025-07-01T18:45:48.748Z] [ACCESS] GET /check-student-auth - 200 - 230ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"230ms","timestamp":"2025-07-01T18:45:48.747Z"}
[2025-07-01T18:45:50.181Z] [ACCESS] GET / - 404 - 55ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"55ms","timestamp":"2025-07-01T18:45:50.180Z"}
[2025-07-01T18:45:50.197Z] [ACCESS] GET / - 404 - 57ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"57ms","timestamp":"2025-07-01T18:45:50.196Z"}
[2025-07-01T18:46:21.648Z] [ACCESS] GET / - 404 - 263ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"263ms","timestamp":"2025-07-01T18:46:21.648Z"}
[2025-07-01T18:47:24.462Z] [ACCESS] GET /check-student-auth - 200 - 229ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"229ms","timestamp":"2025-07-01T18:47:24.462Z"}
[2025-07-01T18:47:26.085Z] [ACCESS] GET /admin/login - 200 - 69ms | {"method":"GET","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"69ms","timestamp":"2025-07-01T18:47:26.084Z"}
[2025-07-01T18:47:28.317Z] [ACCESS] POST /admin/login - 400 - 63ms | {"method":"POST","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":400,"responseTime":"63ms","timestamp":"2025-07-01T18:47:28.316Z"}
[2025-07-01T18:47:31.742Z] [ACCESS] GET / - 404 - 59ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"59ms","timestamp":"2025-07-01T18:47:31.741Z"}
[2025-07-01T18:47:35.891Z] [ACCESS] POST /admin/login - 400 - 57ms | {"method":"POST","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","statusCode":400,"responseTime":"57ms","timestamp":"2025-07-01T18:47:35.890Z"}
[2025-07-01T18:47:53.156Z] [ACCESS] GET / - 304 - 271ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"271ms","timestamp":"2025-07-01T18:47:53.155Z"}
[2025-07-01T18:47:55.470Z] [ACCESS] GET /lessons - 304 - 73ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"73ms","timestamp":"2025-07-01T18:47:55.468Z"}
[2025-07-01T18:47:55.578Z] [ACCESS] GET /check-student-auth - 200 - 68ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"68ms","timestamp":"2025-07-01T18:47:55.578Z"}
[2025-07-01T18:48:03.522Z] [ACCESS] GET /?page=1&limit=10&sort=newest&search=dda - 200 - 677ms | {"method":"GET","url":"/?page=1&limit=10&sort=newest&search=dda","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"677ms","timestamp":"2025-07-01T18:48:03.521Z"}
[2025-07-01T18:48:08.895Z] [ACCESS] GET /?page=1&limit=10&sort=newest&search=vl11 - 200 - 317ms | {"method":"GET","url":"/?page=1&limit=10&sort=newest&search=vl11","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"317ms","timestamp":"2025-07-01T18:48:08.895Z"}
[2025-07-01T18:48:12.759Z] [ACCESS] GET / - 304 - 68ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"68ms","timestamp":"2025-07-01T18:48:12.758Z"}
[2025-07-01T18:48:14.929Z] [ACCESS] GET /quizgame - 200 - 66ms | {"method":"GET","url":"/quizgame","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"66ms","timestamp":"2025-07-01T18:48:14.928Z"}
[2025-07-01T18:48:18.832Z] [ACCESS] GET / - 404 - 68ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"68ms","timestamp":"2025-07-01T18:48:18.831Z"}
[2025-07-01T18:48:23.300Z] [ACCESS] GET /quizgame - 200 - 68ms | {"method":"GET","url":"/quizgame","ip":"::1","userAgent":"Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","statusCode":200,"responseTime":"68ms","timestamp":"2025-07-01T18:48:23.299Z"}
[2025-07-01T18:48:23.381Z] [ACCESS] GET /quizgame - 200 - 67ms | {"method":"GET","url":"/quizgame","ip":"::1","userAgent":"Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","statusCode":200,"responseTime":"67ms","timestamp":"2025-07-01T18:48:23.380Z"}
[2025-07-01T18:48:23.555Z] [ACCESS] GET / - 404 - 271ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"271ms","timestamp":"2025-07-01T18:48:23.554Z"}
[2025-07-01T18:48:23.599Z] [ACCESS] GET / - 404 - 74ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"74ms","timestamp":"2025-07-01T18:48:23.597Z"}
